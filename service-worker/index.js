import { defaultCache } from '@serwist/next/worker'
import {
  Serwist,
  NetworkFirst,
  CacheFirst,
  StaleWhileRevalidate,
} from 'serwist'

const OFFLINE_FALLBACK_PAGE = '/offline.html'

const serwist = new Serwist({
  precacheEntries: self.__SW_MANIFEST || [],
  precacheOptions: {
    cleanupOutdatedCaches: true,
    concurrency: 10,
    fallbackToNetwork: true,
    ignoreURLParametersMatching: [],
  },
  skipWaiting: true,
  clientsClaim: true,
  navigationPreload: true,
  fallbacks: {
    // Make sure the offline page is precached
    document: OFFLINE_FALLBACK_PAGE,
  },
  runtimeCaching: [
    // Add a specific rule for navigation requests
    {
      matcher: ({ request }) => request.mode === 'navigate',
      handler: new NetworkFirst({
        networkTimeoutSeconds: 3,
        cacheName: 'pages-cache',
        plugins: [
          {
            handlerDidError: async () => {
              // Try to return the offline page from the cache
              const cachedResponse = await caches.match(OFFLINE_FALLBACK_PAGE)
              if (cachedResponse) return cachedResponse

              // If offline page is not in cache, try to fetch it
              return fetch(OFFLINE_FALLBACK_PAGE).catch(() => {
                // If all fails, return a simple offline message
                return new Response('You are offline', {
                  status: 503,
                  headers: { 'Content-Type': 'text/html' },
                })
              })
            },
          },
        ],
      }),
    },
    // Specific caching strategy for the downloads page
    {
      matcher: ({ url }) => url.pathname === '/downloads',
      handler: new NetworkFirst({
        networkTimeoutSeconds: 5,
        cacheName: 'downloads-page-cache',
        plugins: [
          {
            handlerDidError: async ({ request }) => {
              console.log(
                'Downloads page network failed, serving from cache or fallback'
              )

              // For the downloads page, try to return it from cache if available
              const cachedResponse = await caches.match('/downloads')
              if (cachedResponse) {
                console.log('Serving downloads page from cache')
                return cachedResponse
              }

              // If this is a navigation request (not an API call), serve the offline page
              if (request.mode === 'navigate') {
                // Try to serve the dedicated offline downloads page
                try {
                  const offlineDownloadsResponse = await caches.match(
                    '/downloads-offline.html'
                  )
                  if (offlineDownloadsResponse) {
                    console.log('Serving offline downloads page')
                    return offlineDownloadsResponse
                  }
                } catch (error) {
                  console.warn('Could not serve offline downloads page:', error)
                }

                // Fallback: try to fetch the offline downloads page
                try {
                  const offlineDownloadsResponse = await fetch(
                    '/downloads-offline.html'
                  )
                  if (offlineDownloadsResponse.ok) {
                    return offlineDownloadsResponse
                  }
                } catch (error) {
                  // Final fallback to the main offline page
                  const offlineResponse = await caches.match(
                    OFFLINE_FALLBACK_PAGE
                  )
                  if (offlineResponse) return offlineResponse

                  return new Response(
                    'Downloads page is not available offline',
                    {
                      status: 503,
                      headers: { 'Content-Type': 'text/html' },
                    }
                  )
                }
              }

              // For non-navigation requests, return a simple error
              return new Response('Network error', {
                status: 503,
                headers: { 'Content-Type': 'text/plain' },
              })
            },
          },
        ],
      }),
    },
    // Enhanced caching for JavaScript chunks and dynamic imports
    {
      matcher: ({ url }) => url.pathname.includes('/_next/static/chunks/'),
      handler: new CacheFirst({
        cacheName: 'next-js-chunks',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              return response.status === 200 ? response : null
            },
          },
        ],
      }),
    },
    // Cache UI components and dynamic imports
    {
      matcher: ({ url, request }) =>
        request.destination === 'script' &&
        (url.pathname.includes('/ui/') ||
          url.pathname.includes('/hooks/') ||
          url.pathname.includes('/utils/')),
      handler: new CacheFirst({
        cacheName: 'ui-components',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              return response.status === 200 ? response : null
            },
          },
        ],
      }),
    },
    // Cache downloads page specific resources
    {
      matcher: ({ url, request }) =>
        request.destination === 'script' &&
        (url.pathname.includes('/pages/downloads') ||
          url.pathname.includes('/hooks/useVideo') ||
          url.pathname.includes('/utils/videoStorage')),
      handler: new CacheFirst({
        cacheName: 'downloads-resources',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              return response.status === 200 ? response : null
            },
          },
        ],
      }),
    },
    // Use StaleWhileRevalidate for CSS and static assets that benefit from freshness
    // but where serving stale content is acceptable
    {
      matcher: ({ url }) =>
        url.pathname.includes('/_next/static/css/') ||
        url.pathname.endsWith('.css'),
      handler: new StaleWhileRevalidate({
        cacheName: 'css-assets',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              return response.status === 200 ? response : null
            },
          },
        ],
      }),
    },
    ...defaultCache,
  ],
})

serwist.addEventListeners()
